"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"1d4ef6e48d4e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzA3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFkNGVmNmU0OGQ0ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/NavBar.tsx":
/*!***********************************!*\
  !*** ./src/components/NavBar.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NavBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Menu!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Menu!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _providers_UserProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/UserProvider */ \"(app-pages-browser)/./src/providers/UserProvider.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(app-pages-browser)/./src/components/ui/navigation-menu.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NavBar(param) {\n    let { className } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { userData } = (0,_providers_UserProvider__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const availableLocales = [\n        \"en\",\n        \"cn\",\n        \"ja\"\n    ];\n    const changeLocale = (newLocale)=>{\n        const newPath = pathname.replace(\"/\".concat(locale), \"/\".concat(newLocale));\n        router.push(newPath);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"flex items-center justify-between p-4 bg-background border border-border rounded-lg shadow-sm\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                            align: \"start\",\n                            className: \"w-52\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/list/public\",\n                                        children: t(\"menu.public\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/list/map\",\n                                        children: t(\"menu.folder\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/about\",\n                                        children: t(\"menu.about\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSub, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSubTrigger, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"i18n\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSubContent, {\n                                            children: availableLocales.map((localeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                    onClick: ()=>changeLocale(localeOption),\n                                                    children: localeOption\n                                                }, localeOption, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/\",\n                className: \"flex items-center space-x-2 text-xl font-semibold\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        src: \"/logo2.png\",\n                        alt: \"Logo\",\n                        width: 32,\n                        height: 32\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Mind Elixir\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/list/public\",\n                                    legacyBehavior: true,\n                                    passHref: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                        className: (0,_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.navigationMenuTriggerStyle)(),\n                                        children: t(\"menu.public\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/list/map\",\n                                    legacyBehavior: true,\n                                    passHref: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                        className: (0,_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.navigationMenuTriggerStyle)(),\n                                        children: t(\"menu.folder\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/about\",\n                                    legacyBehavior: true,\n                                    passHref: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuLink, {\n                                        className: (0,_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.navigationMenuTriggerStyle)(),\n                                        children: t(\"menu.about\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_7__.NavigationMenuItem, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"h-10 px-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"i18n\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                            children: availableLocales.map((localeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                    onClick: ()=>changeLocale(localeOption),\n                                                    children: localeOption\n                                                }, localeOption, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(NavBar, \"AKQB7ybGmIvb/BT4vgdRRzFOw+Q=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useLocale,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _providers_UserProvider__WEBPACK_IMPORTED_MODULE_4__.useUser\n    ];\n});\n_c = NavBar;\nvar _c;\n$RefreshReg$(_c, \"NavBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/NavBar.tsx\n"));

/***/ })

});