'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import connect from '@/connect'
import toast from '@/utils/toast'

interface CreateButtonProps {
  className?: string
}

export default function CreateButton({ className }: CreateButtonProps) {
  const t = useTranslations()
  const router = useRouter()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [title, setTitle] = useState('')
  const [creating, setCreating] = useState(false)

  const handleCreate = async () => {
    if (!title.trim()) return

    setCreating(true)
    try {
      const res = await connect.post('/api/map', {
        name: title,
      })
      setIsModalOpen(false)
      setTitle('')
      router.push(`/edit/${res.data._id}`)
    } catch (error) {
      console.error('Failed to create map:', error)
      toast.error('Failed to create map')
    } finally {
      setCreating(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCreate()
    }
  }

  return (
    <>
      <div
        className={`border-2 border-dashed border-gray-300 hover:border-blue-400 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 hover:from-blue-100/50 hover:to-indigo-100/50 rounded-xl flex justify-center items-center flex-col cursor-pointer transition-all duration-300 group ${className}`}
        onClick={() => setIsModalOpen(true)}
      >
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-3 group-hover:bg-blue-200 transition-colors duration-200">
          <svg
            className="w-8 h-8 text-blue-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </div>
        <div className="text-gray-600 font-medium group-hover:text-blue-600 transition-colors duration-200">
          {t('button.new')}
        </div>
      </div>

      {isModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box bg-white rounded-2xl shadow-2xl border border-gray-100">
            <h3 className="font-bold text-xl text-gray-800 mb-6">{t('misc.title')}</h3>
            <div className="mb-6">
              <input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                onKeyPress={handleKeyPress}
                type="text"
                placeholder="Enter mind map title..."
                className="input input-bordered w-full bg-gray-50 border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:bg-white transition-all duration-200 rounded-xl"
                autoFocus
              />
            </div>
            <div className="modal-action gap-3">
              <button
                className="btn btn-ghost rounded-xl"
                onClick={() => setIsModalOpen(false)}
                disabled={creating}
              >
                {t('misc.cancel')}
              </button>
              <button
                className="btn btn-primary rounded-xl bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700"
                onClick={handleCreate}
                disabled={!title.trim() || creating}
              >
                {creating && <span className="loading loading-spinner loading-sm mr-2"></span>}
                {t('misc.ok')}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
