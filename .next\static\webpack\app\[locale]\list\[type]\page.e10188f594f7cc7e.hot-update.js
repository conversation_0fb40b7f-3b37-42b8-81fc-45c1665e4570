"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/list/[type]/page",{

/***/ "(app-pages-browser)/./src/components/Pagination.tsx":
/*!***************************************!*\
  !*** ./src/components/Pagination.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Pagination; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n\nfunction Pagination(param) {\n    let { page, pageSize, total, onPageChange } = param;\n    const totalPages = Math.ceil(total / pageSize);\n    if (totalPages <= 1) return null;\n    const getVisiblePages = ()=>{\n        const delta = 2;\n        const range = [];\n        const rangeWithDots = [];\n        for(let i = Math.max(2, page - delta); i <= Math.min(totalPages - 1, page + delta); i++){\n            range.push(i);\n        }\n        if (page - delta > 2) {\n            rangeWithDots.push(1, \"...\");\n        } else {\n            rangeWithDots.push(1);\n        }\n        rangeWithDots.push(...range);\n        if (page + delta < totalPages - 1) {\n            rangeWithDots.push(\"...\", totalPages);\n        } else {\n            rangeWithDots.push(totalPages);\n        }\n        return rangeWithDots;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center gap-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"btn btn-ghost btn-sm rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\",\n                onClick: ()=>onPageChange(page - 1),\n                disabled: page <= 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 19l-7-7 7-7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            getVisiblePages().map((pageNum, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"btn btn-sm rounded-lg transition-all duration-200 \".concat(pageNum === page ? \"bg-blue-600 text-white hover:bg-blue-700 border-blue-600\" : pageNum === \"...\" ? \"btn-ghost cursor-default hover:bg-transparent\" : \"btn-ghost hover:bg-gray-100\"),\n                    onClick: ()=>typeof pageNum === \"number\" && onPageChange(pageNum),\n                    disabled: pageNum === \"...\",\n                    children: pageNum\n                }, index, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"btn btn-ghost btn-sm rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\",\n                onClick: ()=>onPageChange(page + 1),\n                disabled: page >= totalPages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 5l7 7-7 7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_c = Pagination;\nvar _c;\n$RefreshReg$(_c, \"Pagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Pagination.tsx\n"));

/***/ })

});