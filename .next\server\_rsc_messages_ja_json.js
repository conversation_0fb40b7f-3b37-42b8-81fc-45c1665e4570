"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_ja_json";
exports.ids = ["_rsc_messages_ja_json"];
exports.modules = {

/***/ "(rsc)/./messages/ja.json":
/*!**************************!*\
  !*** ./messages/ja.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"misc":{"pleaseLogin":"ログインが必要です","networkError":"ネットワークエラー","formatSelect":"ファイル形式","title":"タイトル","ok":"OK","cancel":"キャンセル","login":"ログイン","share":"共有","loggingIn":"ログイン中..."},"menu":{"public":"公開","folder":"フォルダ","about":"基本情報","language":"言語"},"button":{"signin":"ログイン","logout":"ログアウト","signinWithWeibo":"Weibo でログイン","signinWithGitHub":"GitHub でログイン","new":"新規","save":"保存"},"title":{"signinWith":"ログイン方法"},"about":{"1":"Thank you for the free service provided by Netify and Fly.io.","2":"Regularly Data Backup is recommended.","3":"Should you require customization features, please reach <NAME_EMAIL>.","title":"Mind Elixir Cloud について"}}');

/***/ })

};
;