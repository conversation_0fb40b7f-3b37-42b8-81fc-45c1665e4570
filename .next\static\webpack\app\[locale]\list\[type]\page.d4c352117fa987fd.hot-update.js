"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/list/[type]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/list/[type]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/[locale]/list/[type]/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MapListPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/UserProvider */ \"(app-pages-browser)/./src/providers/UserProvider.tsx\");\n/* harmony import */ var _components_SearchBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SearchBar */ \"(app-pages-browser)/./src/components/SearchBar.tsx\");\n/* harmony import */ var _components_LoadingMask__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoadingMask */ \"(app-pages-browser)/./src/components/LoadingMask.tsx\");\n/* harmony import */ var _components_CreateButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/CreateButton */ \"(app-pages-browser)/./src/components/CreateButton.tsx\");\n/* harmony import */ var _components_MindMapCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/MindMapCard */ \"(app-pages-browser)/./src/components/MindMapCard.tsx\");\n/* harmony import */ var _components_Pagination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Pagination */ \"(app-pages-browser)/./src/components/Pagination.tsx\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/connect */ \"(app-pages-browser)/./src/connect.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction MapListPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_11__.useTranslations)();\n    const { userData } = (0,_providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [mapList, setMapList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        total: 0\n    });\n    const type = params.type;\n    const isPublic = type === \"public\";\n    const fetchList = async ()=>{\n        setLoading(true);\n        try {\n            const endpoint = isPublic ? \"/api/public\" : \"/api/map\";\n            const res = await _connect__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(endpoint, {\n                params: {\n                    page: pagination.page,\n                    pageSize: pagination.pageSize,\n                    keyword\n                }\n            });\n            console.log(\"API Response:\", res.data);\n            console.log(\"Map List:\", res.data.list);\n            // 检查API返回的数据结构\n            if (res.data && Array.isArray(res.data.list)) {\n                setMapList(res.data.list);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: res.data.total || 0\n                    }));\n            } else if (res.data && Array.isArray(res.data)) {\n                // 如果API直接返回数组\n                setMapList(res.data);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: res.data.length\n                    }));\n            } else {\n                console.warn(\"Unexpected API response structure:\", res.data);\n                setMapList([]);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch maps:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchList();\n    }, [\n        pagination.page,\n        keyword,\n        type\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"MapList updated:\", mapList, \"Length:\", mapList.length);\n    }, [\n        mapList\n    ]);\n    const handleSearch = (val)=>{\n        setKeyword(val);\n        setPagination((prev)=>({\n                ...prev,\n                page: 1\n            }));\n    };\n    const deleteMap = async (item)=>{\n        if (window.confirm(\"Are you sure to delete this map?\")) {\n            try {\n                await _connect__WEBPACK_IMPORTED_MODULE_9__[\"default\"].delete(\"/api/map/\".concat(item._id));\n                fetchList();\n            } catch (error) {\n                console.error(\"Failed to delete map:\", error);\n            }\n        }\n    };\n    const makePublic = async (item)=>{\n        try {\n            await _connect__WEBPACK_IMPORTED_MODULE_9__[\"default\"].patch(\"/api/map/\".concat(item._id), {\n                public: !item.public\n            });\n            item.public = !item.public;\n            setMapList([\n                ...mapList\n            ]);\n        } catch (error) {\n            console.error(\"Failed to update map:\", error);\n        }\n    };\n    const share = (item)=>{\n        // TODO: Implement share modal\n        console.log(\"Share:\", item);\n    };\n    const download = (item, type)=>{\n        // TODO: Implement download functionality\n        console.log(\"Download:\", item, type);\n    };\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-28\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onSearch: handleSearch\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingMask__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"pt-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"User Data: \",\n                                    userData ? \"Logged in\" : \"Not logged in\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Is Public: \",\n                                    isPublic ? \"Yes\" : \"No\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Map List Length: \",\n                                    mapList.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-20 grid gap-4 grid-cols-1 auto-rows-[208px] sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5\",\n                        children: [\n                            !isPublic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CreateButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 27\n                            }, this),\n                            mapList.map((map)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    href: \"/\".concat(isPublic ? \"share\" : \"edit\", \"/\").concat(map._id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-full\",\n                                        map: map,\n                                        type: isPublic ? \"public\" : \"private\",\n                                        onDelete: ()=>deleteMap(map),\n                                        onDownload: (type)=>download(map, type),\n                                        onMakePublic: ()=>makePublic(map),\n                                        onShare: ()=>share(map)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 17\n                                    }, this)\n                                }, map._id, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center my-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Pagination__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            page: pagination.page,\n                            pageSize: pagination.pageSize,\n                            total: pagination.total,\n                            onPageChange: (page)=>setPagination((prev)=>({\n                                        ...prev,\n                                        page\n                                    }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n    return content;\n}\n_s(MapListPage, \"fiih7hfSvshCt8+KddUlZRJqzxY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_intl__WEBPACK_IMPORTED_MODULE_11__.useTranslations,\n        _providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__.useUser\n    ];\n});\n_c = MapListPage;\nvar _c;\n$RefreshReg$(_c, \"MapListPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/list/[type]/page.tsx\n"));

/***/ })

});