'use client'

import { useTranslations, useLocale } from 'next-intl'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'

interface NavBarProps {
  className?: string
}

export default function NavBar({ className }: NavBarProps) {
  const t = useTranslations()
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()

  const availableLocales = ['en', 'cn', 'ja']

  const changeLocale = (newLocale: string) => {
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`)
    router.push(newPath)
  }

  return (
    <div className={`navbar bg-base-100 ${className}`}>
      <div className="navbar-start">
        <div className="dropdown">
          <label tabIndex={0} className="btn btn-ghost lg:hidden">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 6h16M4 12h8m-8 6h16"
              />
            </svg>
          </label>
          <ul
            tabIndex={0}
            className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow rounded-box w-52"
          >
            <li>
              <Link href="/list/public">{t('menu.public')}</Link>
            </li>
            <li>
              <Link href="/list/map">{t('menu.folder')}</Link>
            </li>
            <li>
              <Link href="/about">{t('menu.about')}</Link>
            </li>
            <li tabIndex={0}>
              <details>
                <summary>i18n</summary>
                <ul className="p-2">
                  {availableLocales.map((localeOption) => (
                    <li key={localeOption} onClick={() => changeLocale(localeOption)}>
                      <a>{localeOption}</a>
                    </li>
                  ))}
                </ul>
              </details>
            </li>
          </ul>
        </div>
        <Link className="btn btn-ghost normal-case text-xl !pl-0" href="/">
          <Image src="/logo2.png" alt="Logo" width={32} height={32} className="h-full" />
          Mind Elixir
        </Link>
      </div>
      <div className="navbar-center hidden lg:flex">
        <ul className="menu menu-horizontal px-1">
          <li>
            <Link href="/list/public">{t('menu.public')}</Link>
          </li>
          <li>
            <Link href="/list/map">{t('menu.folder')}</Link>
          </li>
          <li>
            <Link href="/about">{t('menu.about')}</Link>
          </li>
          <li tabIndex={0}>
            <details>
              <summary>i18n</summary>
              <ul className="p-2">
                {availableLocales.map((localeOption) => (
                  <li key={localeOption} onClick={() => changeLocale(localeOption)}>
                    <a>{localeOption}</a>
                  </li>
                ))}
              </ul>
            </details>
          </li>
        </ul>
      </div>
      <div className="navbar-end">
        {/* teleport dest */}
      </div>
    </div>
  )
}
