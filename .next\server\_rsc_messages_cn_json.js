"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_cn_json";
exports.ids = ["_rsc_messages_cn_json"];
exports.modules = {

/***/ "(rsc)/./messages/cn.json":
/*!**************************!*\
  !*** ./messages/cn.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"misc":{"pleaseLogin":"请点击右上角按钮登陆","networkError":"网络错误","formatSelect":"格式选择","title":"标题","ok":"确定","cancel":"取消","login":"登陆","share":"分享","loggingIn":"正在唤起登录……"},"menu":{"public":"公开","folder":"文件夹","about":"关于","language":"语言"},"button":{"signin":"登录","signinWithWeibo":"使用微博登陆","signinWithGitHub":"使用 GitHub 登陆","logout":"退出登录","new":"新建","save":"保存"},"title":{"signinWith":"登陆方式"},"about":{"1":"感谢 netify 和 fly.io 提供的免费服务","2":"请注意数据备份，编辑好的思维导图请下载归档","3":"接受定制功能，联系邮箱 <EMAIL>","title":"关于 Mind Elixir Cloud"}}');

/***/ })

};
;