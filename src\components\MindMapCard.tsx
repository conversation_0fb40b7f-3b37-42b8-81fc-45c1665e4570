'use client'

import { useState } from 'react'
import dynamic from 'next/dynamic'
import { MindMapItem } from '@/models/list'
import { Options } from 'mind-elixir'

const MindElixirReact = dynamic(() => import('./MindElixirReact'), {
  ssr: false,
  loading: () => <div className="h-full w-full bg-gray-100 animate-pulse"></div>
})

interface MindMapCardProps {
  map: MindMapItem
  type: 'public' | 'private'
  className?: string
  onDelete: () => void
  onDownload: (type: string) => void
  onMakePublic: () => void
  onShare: () => void
}

export default function MindMapCard({
  map,
  type,
  className,
  onDelete,
  onDownload,
  onMakePublic,
  onShare,
}: MindMapCardProps) {
  const [showDropdown, setShowDropdown] = useState(false)

  const options: Options = {
    el: '',
    direction: 2,
    draggable: false,
    editable: false,
    contextMenu: false,
    toolBar: false,
    keypress: false,
  }

  const timeFormatter = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <div className={`card bg-white border border-gray-100 hover:border-gray-200 hover:shadow-lg transition-all duration-300 rounded-xl overflow-hidden group ${className}`}>
      <figure className="w-full aspect-video bg-gradient-to-br from-blue-50 to-indigo-50 relative overflow-hidden">
        <div className="h-full w-full overflow-hidden pointer-events-none">
          <MindElixirReact
            data={map.content}
            options={{
              ...options,
              enableNodeDragging: false,
              enableKeyboard: false,
              enableMouseWheel: false,
              enableEdit: false
            }}
            initScale={0.2}
            className="h-full w-full"
          />
        </div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </figure>
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h2 className="font-semibold text-gray-800 overflow-hidden whitespace-nowrap text-ellipsis flex-1 group-hover:text-blue-600 transition-colors duration-200">
            {map.name}
          </h2>
          {map.public && (
            <span className="badge badge-primary badge-sm ml-2 bg-blue-100 text-blue-700 border-blue-200">
              Public
            </span>
          )}
        </div>
        <p className="text-sm text-gray-500">{timeFormatter(map.updatedAt || map.date)}</p>
      </div>
      
      {type === 'private' && (
        <div className="dropdown dropdown-end absolute right-3 top-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <label
            tabIndex={0}
            className="btn btn-ghost btn-circle btn-sm bg-white/80 backdrop-blur-sm hover:bg-white border border-gray-200 shadow-sm"
            onClick={(e) => {
              e.preventDefault()
              setShowDropdown(!showDropdown)
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-gray-600"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </label>
          {showDropdown && (
            <ul
              tabIndex={0}
              className="dropdown-content z-[1] menu p-2 shadow-lg rounded-xl w-52 bg-white border border-gray-100 backdrop-blur-sm"
              onClick={(e) => e.preventDefault()}
            >
              <li onClick={onMakePublic}>
                <a>{map.public ? 'Make Private' : 'Make Public'}</a>
              </li>
              <li onClick={onShare}>
                <a>Share</a>
              </li>
              <li>
                <details>
                  <summary>Download</summary>
                  <ul className="p-2">
                    <li onClick={() => onDownload('json')}>
                      <a>JSON</a>
                    </li>
                    <li onClick={() => onDownload('html')}>
                      <a>HTML</a>
                    </li>
                    <li onClick={() => onDownload('xmind')}>
                      <a>XMind</a>
                    </li>
                  </ul>
                </details>
              </li>
              <li onClick={onDelete}>
                <a className="text-error">Delete</a>
              </li>
            </ul>
          )}
        </div>
      )}
    </div>
  )
}
