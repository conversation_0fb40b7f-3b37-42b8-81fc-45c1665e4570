"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+icu-messageformat-parser@2.11.2";
exports.ids = ["vendor-chunks/@formatjs+icu-messageformat-parser@2.11.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBestPattern: () => (/* binding */ getBestPattern)\n/* harmony export */ });\n/* harmony import */ var _time_data_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time-data.generated */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\");\n\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[regionTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[languageTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[\"\".concat(languageTag, \"-001\")] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData['001'];\n    return hourCycles[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorKind: () => (/* binding */ ErrorKind)\n/* harmony export */ });\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/index.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.TYPE),\n/* harmony export */   _Parser: () => (/* binding */ _Parser),\n/* harmony export */   createLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isArgumentElement),\n/* harmony export */   isDateElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement),\n/* harmony export */   isPoundElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPoundElement),\n/* harmony export */   isSelectElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement),\n/* harmony export */   isStructurallySame: () => (/* reexport safe */ _manipulator__WEBPACK_IMPORTED_MODULE_4__.isStructurallySame),\n/* harmony export */   isTagElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTagElement),\n/* harmony export */   isTimeElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _manipulator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./manipulator */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\");\n\n\n\n\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement)(el) && (0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement)(el)) &&\n            (0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new _parser__WEBPACK_IMPORTED_MODULE_1__.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\n\n// only for testing\nvar _Parser = _parser__WEBPACK_IMPORTED_MODULE_1__.Parser;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hoistSelectors: () => (/* binding */ hoistSelectors),\n/* harmony export */   isStructurallySame: () => (/* binding */ isStructurallySame)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n\n\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return (0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nfunction hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nfunction isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[type], \" vs \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regex.generated */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\");\n/* harmony import */ var _formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formatjs/icu-skeleton-parser */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\");\n/* harmony import */ var _date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./date-time-pattern-generator */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\");\nvar _a;\n\n\n\n\n\n\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0,_date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number\n                            : argType === 'date'\n                                ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date\n                                : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\n\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE_SEPARATOR_REGEX: () => (/* binding */ SPACE_SEPARATOR_REGEX),\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjIvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3JlZ2V4LmdlbmVyYXRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL21pbmQtZWxpeGlyLWNsb3VkLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bmb3JtYXRqcytpY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXJAMi4xMS4yL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanM/ZGQyNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAZ2VuZXJhdGVkIGZyb20gcmVnZXgtZ2VuLnRzXG5leHBvcnQgdmFyIFNQQUNFX1NFUEFSQVRPUl9SRUdFWCA9IC9bIFxceEEwXFx1MTY4MFxcdTIwMDAtXFx1MjAwQVxcdTIwMkZcXHUyMDVGXFx1MzAwMF0vO1xuZXhwb3J0IHZhciBXSElURV9TUEFDRV9SRUdFWCA9IC9bXFx0LVxcciBcXHg4NVxcdTIwMEVcXHUyMDBGXFx1MjAyOFxcdTIwMjldLztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeData: () => (/* binding */ timeData)\n/* harmony export */ });\n// @generated from time-data-gen.ts\n// prettier-ignore  \nvar timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjIvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3RpbWUtZGF0YS5nZW5lcmF0ZWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjIvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3RpbWUtZGF0YS5nZW5lcmF0ZWQuanM/YjY1ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAZ2VuZXJhdGVkIGZyb20gdGltZS1kYXRhLWdlbi50c1xuLy8gcHJldHRpZXItaWdub3JlICBcbmV4cG9ydCB2YXIgdGltZURhdGEgPSB7XG4gICAgXCIwMDFcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiNDE5XCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJBQ1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQURcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkFFXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJBRlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiQUdcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkFJXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJBTFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkFNXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJBT1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQVJcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkFTXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkFUXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJBVVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQVdcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkFYXCI6IFtcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiQVpcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJCQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkJCXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJCRFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkJFXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJCRlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQkdcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJCSFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiQklcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiQkpcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkJMXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJCTVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQk5cIjogW1xuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkJPXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJCUVwiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkJSXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJCU1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQlRcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiQldcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkJZXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkJaXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJDQVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQ0NcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkNEXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJDRlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkNHXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJDSFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkNJXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJDS1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQ0xcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkNNXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQ05cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkNPXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJDUFwiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkNSXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJDVVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiQ1ZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkNXXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJDWFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiQ1lcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkNaXCI6IFtcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiREVcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkRHXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJESlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJES1wiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkRNXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJET1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiRFpcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkVBXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJFQ1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiRUVcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkVHXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJFSFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiRVJcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiRVNcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkVUXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJGSVwiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkZKXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJGS1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiRk1cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkZPXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkZSXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJHQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR0JcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkdEXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJHRVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkdGXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJHR1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR0hcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiR0lcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkdMXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIkdNXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJHTlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR1BcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkdRXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJHUlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR1RcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkdVXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJHV1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiR1lcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkhLXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJITlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiSFJcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkhVXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIklDXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJJRFwiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIklFXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJJTFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiSU1cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIklOXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIklPXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJJUVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiSVJcIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIklTXCI6IFtcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiSVRcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkpFXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJKTVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiSk9cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIkpQXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiS1wiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJLRVwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiS0dcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIktIXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJLSVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiS01cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIktOXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJLUFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiS1JcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIktXXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJLWVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiS1pcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkxBXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJMQlwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiTENcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIkxJXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTEtcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIkxSXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJMU1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJMVFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTFVcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJMVlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTFlcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIk1BXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJNQ1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTURcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1FXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTUZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1HXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIk1IXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJNS1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTUxcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJNTVwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTU5cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1PXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJNUFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTVFcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1SXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJNU1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTVRcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTVVcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTVZcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTVdcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk1YXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJNWVwiOiBbXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiTVpcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk5BXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJOQ1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTkVcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJORlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTkdcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk5JXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJOTFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTk9cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiTlBcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJOUlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiTlVcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIk5aXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJPTVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiUEFcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIlBFXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJQRlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlBHXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlBIXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJQS1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlBMXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIlBNXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJQTlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiUFJcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIlBTXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJQVFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiUFdcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiUFlcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIlFBXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJSRVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiUk9cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlJTXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiUlVcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJSV1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJTQVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiU0JcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlNDXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiU0RcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlNFXCI6IFtcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiU0dcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlNIXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJTSVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiU0pcIjogW1xuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJTS1wiOiBbXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlNMXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJTTVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlNOXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiU09cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiU1JcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlNTXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJTVFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiU1ZcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcIlNYXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJTWVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwiU1pcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlRBXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJUQ1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVERcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJURlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlRHXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJUSFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJUSlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJUTFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiVE1cIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCJcbiAgICBdLFxuICAgIFwiVE5cIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlRPXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlRSXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJUVFwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVFdcIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIlRaXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJVQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIlVHXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJVTVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVVNcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlVZXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJVWlwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIlZBXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVkNcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlZFXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJWR1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiVklcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlZOXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIlZVXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIldGXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJXU1wiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJYS1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiXG4gICAgXSxcbiAgICBcIllFXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJZVFwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiWkFcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcIlpNXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJaV1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIlxuICAgIF0sXG4gICAgXCJhZi1aQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiYXItMDAxXCI6IFtcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJjYS1FU1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcImVuLTAwMVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiZW4tSEtcIjogW1xuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoYlwiLFxuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcImVuLUlMXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJlbi1NWVwiOiBbXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiZXMtQlJcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoYlwiXG4gICAgXSxcbiAgICBcImVzLUVTXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIlxuICAgIF0sXG4gICAgXCJlcy1HUVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCJcbiAgICBdLFxuICAgIFwiZnItQ0FcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJnbC1FU1wiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJoQlwiXG4gICAgXSxcbiAgICBcImd1LUlOXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJoaS1JTlwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIml0LUNIXCI6IFtcbiAgICAgICAgXCJIXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcImhCXCJcbiAgICBdLFxuICAgIFwiaXQtSVRcIjogW1xuICAgICAgICBcIkhcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaEJcIlxuICAgIF0sXG4gICAgXCJrbi1JTlwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcIm1sLUlOXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwibXItSU5cIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiSFwiXG4gICAgXSxcbiAgICBcInBhLUlOXCI6IFtcbiAgICAgICAgXCJoQlwiLFxuICAgICAgICBcImhiXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJ0YS1JTlwiOiBbXG4gICAgICAgIFwiaEJcIixcbiAgICAgICAgXCJoXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJIXCJcbiAgICBdLFxuICAgIFwidGUtSU5cIjogW1xuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaFwiLFxuICAgICAgICBcIkhcIlxuICAgIF0sXG4gICAgXCJ6dS1aQVwiOiBbXG4gICAgICAgIFwiSFwiLFxuICAgICAgICBcImhCXCIsXG4gICAgICAgIFwiaGJcIixcbiAgICAgICAgXCJoXCJcbiAgICBdXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* binding */ SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* binding */ TYPE),\n/* harmony export */   createLiteralElement: () => (/* binding */ createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* binding */ createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* binding */ isArgumentElement),\n/* harmony export */   isDateElement: () => (/* binding */ isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* binding */ isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* binding */ isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* binding */ isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* binding */ isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* binding */ isPluralElement),\n/* harmony export */   isPoundElement: () => (/* binding */ isPoundElement),\n/* harmony export */   isSelectElement: () => (/* binding */ isSelectElement),\n/* harmony export */   isTagElement: () => (/* binding */ isTagElement),\n/* harmony export */   isTimeElement: () => (/* binding */ isTimeElement)\n/* harmony export */ });\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBestPattern: () => (/* binding */ getBestPattern)\n/* harmony export */ });\n/* harmony import */ var _time_data_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time-data.generated */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\");\n\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[regionTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[languageTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[\"\".concat(languageTag, \"-001\")] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData['001'];\n    return hourCycles[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorKind: () => (/* binding */ ErrorKind)\n/* harmony export */ });\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjIvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL2Vycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0Esc0NBQXNDLEtBQUs7QUFDM0M7QUFDQSx3Q0FBd0MsS0FBSztBQUM3QztBQUNBLDBDQUEwQyxRQUFRO0FBQ2xEO0FBQ0EseUNBQXlDLGNBQWM7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCxnQkFBZ0I7QUFDNUU7QUFDQSwrREFBK0QsY0FBYztBQUM3RTtBQUNBLDREQUE0RDtBQUM1RDtBQUNBLGdEQUFnRCxZQUFZO0FBQzVEO0FBQ0EsaUZBQWlGLG9CQUFvQjtBQUNyRztBQUNBLHVFQUF1RSx1QkFBdUI7QUFDOUY7QUFDQSx5REFBeUQsWUFBWTtBQUNyRTtBQUNBLDRFQUE0RSxZQUFZO0FBQ3hGO0FBQ0EseUVBQXlFLG1CQUFtQjtBQUM1RjtBQUNBO0FBQ0E7QUFDQSxlQUFlLGlCQUFpQjtBQUNoQztBQUNBO0FBQ0EscUVBQXFFLGlCQUFpQixHQUFHO0FBQ3pGO0FBQ0E7QUFDQTtBQUNBLGNBQWMsa0JBQWtCLEdBQUcsS0FBSyxHQUFHO0FBQzNDO0FBQ0E7QUFDQTtBQUNBLGNBQWMsb0JBQW9CLE9BQU8sT0FBTyxPQUFPO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOEJBQThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjIvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL2Vycm9yLmpzPzBkYmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBFcnJvcktpbmQ7XG4oZnVuY3Rpb24gKEVycm9yS2luZCkge1xuICAgIC8qKiBBcmd1bWVudCBpcyB1bmNsb3NlZCAoZS5nLiBgezBgKSAqL1xuICAgIEVycm9yS2luZFtFcnJvcktpbmRbXCJFWFBFQ1RfQVJHVU1FTlRfQ0xPU0lOR19CUkFDRVwiXSA9IDFdID0gXCJFWFBFQ1RfQVJHVU1FTlRfQ0xPU0lOR19CUkFDRVwiO1xuICAgIC8qKiBBcmd1bWVudCBpcyBlbXB0eSAoZS5nLiBge31gKS4gKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiRU1QVFlfQVJHVU1FTlRcIl0gPSAyXSA9IFwiRU1QVFlfQVJHVU1FTlRcIjtcbiAgICAvKiogQXJndW1lbnQgaXMgbWFsZm9ybWVkIChlLmcuIGB7Zm9vIX1gYCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiTUFMRk9STUVEX0FSR1VNRU5UXCJdID0gM10gPSBcIk1BTEZPUk1FRF9BUkdVTUVOVFwiO1xuICAgIC8qKiBFeHBlY3QgYW4gYXJndW1lbnQgdHlwZSAoZS5nLiBge2Zvbyx9YCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiRVhQRUNUX0FSR1VNRU5UX1RZUEVcIl0gPSA0XSA9IFwiRVhQRUNUX0FSR1VNRU5UX1RZUEVcIjtcbiAgICAvKiogVW5zdXBwb3J0ZWQgYXJndW1lbnQgdHlwZSAoZS5nLiBge2Zvbyxmb299YCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiSU5WQUxJRF9BUkdVTUVOVF9UWVBFXCJdID0gNV0gPSBcIklOVkFMSURfQVJHVU1FTlRfVFlQRVwiO1xuICAgIC8qKiBFeHBlY3QgYW4gYXJndW1lbnQgc3R5bGUgKGUuZy4gYHtmb28sIG51bWJlciwgfWApICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIkVYUEVDVF9BUkdVTUVOVF9TVFlMRVwiXSA9IDZdID0gXCJFWFBFQ1RfQVJHVU1FTlRfU1RZTEVcIjtcbiAgICAvKiogVGhlIG51bWJlciBza2VsZXRvbiBpcyBpbnZhbGlkLiAqL1xuICAgIEVycm9yS2luZFtFcnJvcktpbmRbXCJJTlZBTElEX05VTUJFUl9TS0VMRVRPTlwiXSA9IDddID0gXCJJTlZBTElEX05VTUJFUl9TS0VMRVRPTlwiO1xuICAgIC8qKiBUaGUgZGF0ZSB0aW1lIHNrZWxldG9uIGlzIGludmFsaWQuICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIklOVkFMSURfREFURV9USU1FX1NLRUxFVE9OXCJdID0gOF0gPSBcIklOVkFMSURfREFURV9USU1FX1NLRUxFVE9OXCI7XG4gICAgLyoqIEV4ZXBjdCBhIG51bWJlciBza2VsZXRvbiBmb2xsb3dpbmcgdGhlIGA6OmAgKGUuZy4gYHtmb28sIG51bWJlciwgOjp9YCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiRVhQRUNUX05VTUJFUl9TS0VMRVRPTlwiXSA9IDldID0gXCJFWFBFQ1RfTlVNQkVSX1NLRUxFVE9OXCI7XG4gICAgLyoqIEV4ZXBjdCBhIGRhdGUgdGltZSBza2VsZXRvbiBmb2xsb3dpbmcgdGhlIGA6OmAgKGUuZy4gYHtmb28sIGRhdGUsIDo6fWApICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIkVYUEVDVF9EQVRFX1RJTUVfU0tFTEVUT05cIl0gPSAxMF0gPSBcIkVYUEVDVF9EQVRFX1RJTUVfU0tFTEVUT05cIjtcbiAgICAvKiogVW5tYXRjaGVkIGFwb3N0cm9waGVzIGluIHRoZSBhcmd1bWVudCBzdHlsZSAoZS5nLiBge2ZvbywgbnVtYmVyLCAndGVzdGApICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIlVOQ0xPU0VEX1FVT1RFX0lOX0FSR1VNRU5UX1NUWUxFXCJdID0gMTFdID0gXCJVTkNMT1NFRF9RVU9URV9JTl9BUkdVTUVOVF9TVFlMRVwiO1xuICAgIC8qKiBNaXNzaW5nIHNlbGVjdCBhcmd1bWVudCBvcHRpb25zIChlLmcuIGB7Zm9vLCBzZWxlY3R9YCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiRVhQRUNUX1NFTEVDVF9BUkdVTUVOVF9PUFRJT05TXCJdID0gMTJdID0gXCJFWFBFQ1RfU0VMRUNUX0FSR1VNRU5UX09QVElPTlNcIjtcbiAgICAvKiogRXhwZWN0aW5nIGFuIG9mZnNldCB2YWx1ZSBpbiBgcGx1cmFsYCBvciBgc2VsZWN0b3JkaW5hbGAgYXJndW1lbnQgKGUuZyBge2ZvbywgcGx1cmFsLCBvZmZzZXR9YCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiRVhQRUNUX1BMVVJBTF9BUkdVTUVOVF9PRkZTRVRfVkFMVUVcIl0gPSAxM10gPSBcIkVYUEVDVF9QTFVSQUxfQVJHVU1FTlRfT0ZGU0VUX1ZBTFVFXCI7XG4gICAgLyoqIE9mZnNldCB2YWx1ZSBpbiBgcGx1cmFsYCBvciBgc2VsZWN0b3JkaW5hbGAgaXMgaW52YWxpZCAoZS5nLiBge2ZvbywgcGx1cmFsLCBvZmZzZXQ6IHh9YCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiSU5WQUxJRF9QTFVSQUxfQVJHVU1FTlRfT0ZGU0VUX1ZBTFVFXCJdID0gMTRdID0gXCJJTlZBTElEX1BMVVJBTF9BUkdVTUVOVF9PRkZTRVRfVkFMVUVcIjtcbiAgICAvKiogRXhwZWN0aW5nIGEgc2VsZWN0b3IgaW4gYHNlbGVjdGAgYXJndW1lbnQgKGUuZyBge2Zvbywgc2VsZWN0fWApICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIkVYUEVDVF9TRUxFQ1RfQVJHVU1FTlRfU0VMRUNUT1JcIl0gPSAxNV0gPSBcIkVYUEVDVF9TRUxFQ1RfQVJHVU1FTlRfU0VMRUNUT1JcIjtcbiAgICAvKiogRXhwZWN0aW5nIGEgc2VsZWN0b3IgaW4gYHBsdXJhbGAgb3IgYHNlbGVjdG9yZGluYWxgIGFyZ3VtZW50IChlLmcgYHtmb28sIHBsdXJhbH1gKSAqL1xuICAgIEVycm9yS2luZFtFcnJvcktpbmRbXCJFWFBFQ1RfUExVUkFMX0FSR1VNRU5UX1NFTEVDVE9SXCJdID0gMTZdID0gXCJFWFBFQ1RfUExVUkFMX0FSR1VNRU5UX1NFTEVDVE9SXCI7XG4gICAgLyoqIEV4cGVjdGluZyBhIG1lc3NhZ2UgZnJhZ21lbnQgYWZ0ZXIgdGhlIGBzZWxlY3RgIHNlbGVjdG9yIChlLmcuIGB7Zm9vLCBzZWxlY3QsIGFwcGxlfWApICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIkVYUEVDVF9TRUxFQ1RfQVJHVU1FTlRfU0VMRUNUT1JfRlJBR01FTlRcIl0gPSAxN10gPSBcIkVYUEVDVF9TRUxFQ1RfQVJHVU1FTlRfU0VMRUNUT1JfRlJBR01FTlRcIjtcbiAgICAvKipcbiAgICAgKiBFeHBlY3RpbmcgYSBtZXNzYWdlIGZyYWdtZW50IGFmdGVyIHRoZSBgcGx1cmFsYCBvciBgc2VsZWN0b3JkaW5hbGAgc2VsZWN0b3JcbiAgICAgKiAoZS5nLiBge2ZvbywgcGx1cmFsLCBvbmV9YClcbiAgICAgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiRVhQRUNUX1BMVVJBTF9BUkdVTUVOVF9TRUxFQ1RPUl9GUkFHTUVOVFwiXSA9IDE4XSA9IFwiRVhQRUNUX1BMVVJBTF9BUkdVTUVOVF9TRUxFQ1RPUl9GUkFHTUVOVFwiO1xuICAgIC8qKiBTZWxlY3RvciBpbiBgcGx1cmFsYCBvciBgc2VsZWN0b3JkaW5hbGAgaXMgbWFsZm9ybWVkIChlLmcuIGB7Zm9vLCBwbHVyYWwsID14IHsjfX1gKSAqL1xuICAgIEVycm9yS2luZFtFcnJvcktpbmRbXCJJTlZBTElEX1BMVVJBTF9BUkdVTUVOVF9TRUxFQ1RPUlwiXSA9IDE5XSA9IFwiSU5WQUxJRF9QTFVSQUxfQVJHVU1FTlRfU0VMRUNUT1JcIjtcbiAgICAvKipcbiAgICAgKiBEdXBsaWNhdGUgc2VsZWN0b3JzIGluIGBwbHVyYWxgIG9yIGBzZWxlY3RvcmRpbmFsYCBhcmd1bWVudC5cbiAgICAgKiAoZS5nLiB7Zm9vLCBwbHVyYWwsIG9uZSB7I30gb25lIHsjfX0pXG4gICAgICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIkRVUExJQ0FURV9QTFVSQUxfQVJHVU1FTlRfU0VMRUNUT1JcIl0gPSAyMF0gPSBcIkRVUExJQ0FURV9QTFVSQUxfQVJHVU1FTlRfU0VMRUNUT1JcIjtcbiAgICAvKiogRHVwbGljYXRlIHNlbGVjdG9ycyBpbiBgc2VsZWN0YCBhcmd1bWVudC5cbiAgICAgKiAoZS5nLiB7Zm9vLCBzZWxlY3QsIGFwcGxlIHthcHBsZX0gYXBwbGUge2FwcGxlfX0pXG4gICAgICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIkRVUExJQ0FURV9TRUxFQ1RfQVJHVU1FTlRfU0VMRUNUT1JcIl0gPSAyMV0gPSBcIkRVUExJQ0FURV9TRUxFQ1RfQVJHVU1FTlRfU0VMRUNUT1JcIjtcbiAgICAvKiogUGx1cmFsIG9yIHNlbGVjdCBhcmd1bWVudCBvcHRpb24gbXVzdCBoYXZlIGBvdGhlcmAgY2xhdXNlLiAqL1xuICAgIEVycm9yS2luZFtFcnJvcktpbmRbXCJNSVNTSU5HX09USEVSX0NMQVVTRVwiXSA9IDIyXSA9IFwiTUlTU0lOR19PVEhFUl9DTEFVU0VcIjtcbiAgICAvKiogVGhlIHRhZyBpcyBtYWxmb3JtZWQuIChlLmcuIGA8Ym9sZCE+Zm9vPC9ib2xkIT4pICovXG4gICAgRXJyb3JLaW5kW0Vycm9yS2luZFtcIklOVkFMSURfVEFHXCJdID0gMjNdID0gXCJJTlZBTElEX1RBR1wiO1xuICAgIC8qKiBUaGUgdGFnIG5hbWUgaXMgaW52YWxpZC4gKGUuZy4gYDwxMjM+Zm9vPC8xMjM+YCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiSU5WQUxJRF9UQUdfTkFNRVwiXSA9IDI1XSA9IFwiSU5WQUxJRF9UQUdfTkFNRVwiO1xuICAgIC8qKiBUaGUgY2xvc2luZyB0YWcgZG9lcyBub3QgbWF0Y2ggdGhlIG9wZW5pbmcgdGFnLiAoZS5nLiBgPGJvbGQ+Zm9vPC9pdGFsaWM+YCkgKi9cbiAgICBFcnJvcktpbmRbRXJyb3JLaW5kW1wiVU5NQVRDSEVEX0NMT1NJTkdfVEFHXCJdID0gMjZdID0gXCJVTk1BVENIRURfQ0xPU0lOR19UQUdcIjtcbiAgICAvKiogVGhlIG9wZW5pbmcgdGFnIGhhcyB1bm1hdGNoZWQgY2xvc2luZyB0YWcuIChlLmcuIGA8Ym9sZD5mb29gKSAqL1xuICAgIEVycm9yS2luZFtFcnJvcktpbmRbXCJVTkNMT1NFRF9UQUdcIl0gPSAyN10gPSBcIlVOQ0xPU0VEX1RBR1wiO1xufSkoRXJyb3JLaW5kIHx8IChFcnJvcktpbmQgPSB7fSkpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/index.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.TYPE),\n/* harmony export */   _Parser: () => (/* binding */ _Parser),\n/* harmony export */   createLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isArgumentElement),\n/* harmony export */   isDateElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement),\n/* harmony export */   isPoundElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPoundElement),\n/* harmony export */   isSelectElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement),\n/* harmony export */   isStructurallySame: () => (/* reexport safe */ _manipulator__WEBPACK_IMPORTED_MODULE_4__.isStructurallySame),\n/* harmony export */   isTagElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTagElement),\n/* harmony export */   isTimeElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _manipulator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./manipulator */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\");\n\n\n\n\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement)(el) && (0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement)(el)) &&\n            (0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new _parser__WEBPACK_IMPORTED_MODULE_1__.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\n\n// only for testing\nvar _Parser = _parser__WEBPACK_IMPORTED_MODULE_1__.Parser;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hoistSelectors: () => (/* binding */ hoistSelectors),\n/* harmony export */   isStructurallySame: () => (/* binding */ isStructurallySame)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n\n\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return (0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nfunction hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nfunction isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[type], \" vs \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regex.generated */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\");\n/* harmony import */ var _formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formatjs/icu-skeleton-parser */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\");\n/* harmony import */ var _date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./date-time-pattern-generator */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\");\nvar _a;\n\n\n\n\n\n\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0,_date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number\n                            : argType === 'date'\n                                ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date\n                                : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\n\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE_SEPARATOR_REGEX: () => (/* binding */ SPACE_SEPARATOR_REGEX),\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjIvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3JlZ2V4LmdlbmVyYXRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL21pbmQtZWxpeGlyLWNsb3VkLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bmb3JtYXRqcytpY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXJAMi4xMS4yL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanM/YWFmNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAZ2VuZXJhdGVkIGZyb20gcmVnZXgtZ2VuLnRzXG5leHBvcnQgdmFyIFNQQUNFX1NFUEFSQVRPUl9SRUdFWCA9IC9bIFxceEEwXFx1MTY4MFxcdTIwMDAtXFx1MjAwQVxcdTIwMkZcXHUyMDVGXFx1MzAwMF0vO1xuZXhwb3J0IHZhciBXSElURV9TUEFDRV9SRUdFWCA9IC9bXFx0LVxcciBcXHg4NVxcdTIwMEVcXHUyMDBGXFx1MjAyOFxcdTIwMjldLztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeData: () => (/* binding */ timeData)\n/* harmony export */ });\n// @generated from time-data-gen.ts\n// prettier-ignore  \nvar timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* binding */ SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* binding */ TYPE),\n/* harmony export */   createLiteralElement: () => (/* binding */ createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* binding */ createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* binding */ isArgumentElement),\n/* harmony export */   isDateElement: () => (/* binding */ isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* binding */ isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* binding */ isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* binding */ isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* binding */ isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* binding */ isPluralElement),\n/* harmony export */   isPoundElement: () => (/* binding */ isPoundElement),\n/* harmony export */   isSelectElement: () => (/* binding */ isSelectElement),\n/* harmony export */   isTagElement: () => (/* binding */ isTagElement),\n/* harmony export */   isTimeElement: () => (/* binding */ isTimeElement)\n/* harmony export */ });\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\n");

/***/ })

};
;