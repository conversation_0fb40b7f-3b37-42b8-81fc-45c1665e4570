'use client'

import { useTranslations } from 'next-intl'
import { useUser } from '@/providers/UserProvider'

export default function LogoutButton() {
  const t = useTranslations('button')
  const { userData, logout } = useUser()

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  if (!userData) return null

  return (
    <div className="dropdown dropdown-end">
      <label tabIndex={0} className="btn btn-ghost btn-circle avatar hover:bg-gray-100 transition-colors duration-200">
        <div className="w-10 h-10 rounded-full ring-2 ring-gray-200 hover:ring-blue-300 transition-all duration-200">
          <img src={userData.avatar} alt="Avatar" className="rounded-full object-cover" />
        </div>
      </label>
      <ul
        tabIndex={0}
        className="mt-3 z-[1] p-2 shadow-lg menu menu-sm dropdown-content rounded-xl w-52 bg-white border border-gray-100 backdrop-blur-sm"
      >
        <li onClick={handleLogout}>
          <a className="flex items-center gap-3 p-3 hover:bg-red-50 hover:text-red-600 rounded-lg transition-colors duration-200">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            {t('logout')}
          </a>
        </li>
      </ul>
    </div>
  )
}
