import type { Metadata } from 'next'
import '../globals.css'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import NavBar from '@/components/NavBar'
import { UserProvider } from '@/providers/UserProvider'

export const metadata: Metadata = {
  title: 'Mind Elixir Cloud',
  description: 'A powerful mind mapping application',
}

export default async function RootLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  const messages = await getMessages()

  return (
    <html lang={locale}>
      <body className="font-sans">
        <NextIntlClientProvider messages={messages}>
          <UserProvider>
            <div className="absolute top-0 w-screen z-10 my-5 pointer-events-none">
              <NavBar className="w-3/5 m-auto rounded drop-shadow pointer-events-auto" />
            </div>
            {children}
          </UserProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
