"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/list/[type]/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.511.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js ***!
  \***********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ChevronLeft; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m15 18-6-6 6-6\",\n            key: \"1wnfg3\"\n        }\n    ]\n];\nconst ChevronLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-left\", __iconNode);\n //# sourceMappingURL=chevron-left.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Pagination.tsx":
/*!***************************************!*\
  !*** ./src/components/Pagination.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Pagination; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\n\n\nfunction Pagination(param) {\n    let { page, pageSize, total, onPageChange } = param;\n    const totalPages = Math.ceil(total / pageSize);\n    if (totalPages <= 1) return null;\n    const getVisiblePages = ()=>{\n        const delta = 2;\n        const range = [];\n        const rangeWithDots = [];\n        for(let i = Math.max(2, page - delta); i <= Math.min(totalPages - 1, page + delta); i++){\n            range.push(i);\n        }\n        if (page - delta > 2) {\n            rangeWithDots.push(1, \"...\");\n        } else {\n            rangeWithDots.push(1);\n        }\n        rangeWithDots.push(...range);\n        if (page + delta < totalPages - 1) {\n            rangeWithDots.push(\"...\", totalPages);\n        } else {\n            rangeWithDots.push(totalPages);\n        }\n        return rangeWithDots;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center gap-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: ()=>onPageChange(page - 1),\n                disabled: page <= 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            getVisiblePages().map((pageNum, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                    variant: pageNum === page ? \"default\" : \"ghost\",\n                    size: \"sm\",\n                    onClick: ()=>typeof pageNum === \"number\" && onPageChange(pageNum),\n                    disabled: pageNum === \"...\",\n                    className: pageNum === \"...\" ? \"cursor-default\" : \"\",\n                    children: pageNum\n                }, index, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: ()=>onPageChange(page + 1),\n                disabled: page >= totalPages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_c = Pagination;\nvar _c;\n$RefreshReg$(_c, \"Pagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Pagination.tsx\n"));

/***/ })

});