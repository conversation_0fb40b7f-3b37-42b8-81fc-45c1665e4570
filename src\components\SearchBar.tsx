'use client'

interface SearchBarProps {
  onSearch: (value: string) => void
  className?: string
}

export default function SearchBar({ onSearch, className }: SearchBarProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearch(e.target.value)
  }

  return (
    <div className={`max-w-lg mx-auto mb-8 ${className}`}>
      <div className="relative">
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        <input
          className="input input-bordered w-full pl-12 pr-4 py-3 bg-white/80 backdrop-blur-sm border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:bg-white transition-all duration-200 rounded-xl shadow-sm"
          type="text"
          id="search"
          placeholder="Search mind maps..."
          onChange={handleChange}
        />
      </div>
    </div>
  )
}
