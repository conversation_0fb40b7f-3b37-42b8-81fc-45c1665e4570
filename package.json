{"name": "mind-elixir-cloud", "version": "0.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mind-elixir/export-html": "^3.0.0", "@mind-elixir/export-xmind": "^2.0.1", "@mind-elixir/node-menu-neo": "^1.0.3", "axios": "^1.4.0", "dompurify": "^3.0.8", "file-saver": "^2.0.5", "mind-elixir": "5.0.0-beta.21", "next": "^14.0.0", "next-intl": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.9", "@types/dompurify": "^3.0.5", "@types/file-saver": "^2.0.5", "@types/node": "^20.4.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.14", "daisyui": "^3.2.1", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.25", "tailwindcss": "^3.3.2", "typescript": "^5.0.2"}}