"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-menu@2.1.15_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3.2_apm2kfxcvbaumwrlajepl7cp2e";
exports.ids = ["vendor-chunks/@radix-ui+react-menu@2.1.15_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3.2_apm2kfxcvbaumwrlajepl7cp2e"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.15_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3.2_apm2kfxcvbaumwrlajepl7cp2e/node_modules/@radix-ui/react-menu/dist/index.mjs":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-menu@2.1.15_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3.2_apm2kfxcvbaumwrlajepl7cp2e/node_modules/@radix-ui/react-menu/dist/index.mjs ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   MenuAnchor: () => (/* binding */ MenuAnchor),\n/* harmony export */   MenuArrow: () => (/* binding */ MenuArrow),\n/* harmony export */   MenuCheckboxItem: () => (/* binding */ MenuCheckboxItem),\n/* harmony export */   MenuContent: () => (/* binding */ MenuContent),\n/* harmony export */   MenuGroup: () => (/* binding */ MenuGroup),\n/* harmony export */   MenuItem: () => (/* binding */ MenuItem),\n/* harmony export */   MenuItemIndicator: () => (/* binding */ MenuItemIndicator),\n/* harmony export */   MenuLabel: () => (/* binding */ MenuLabel),\n/* harmony export */   MenuPortal: () => (/* binding */ MenuPortal),\n/* harmony export */   MenuRadioGroup: () => (/* binding */ MenuRadioGroup),\n/* harmony export */   MenuRadioItem: () => (/* binding */ MenuRadioItem),\n/* harmony export */   MenuSeparator: () => (/* binding */ MenuSeparator),\n/* harmony export */   MenuSub: () => (/* binding */ MenuSub),\n/* harmony export */   MenuSubContent: () => (/* binding */ MenuSubContent),\n/* harmony export */   MenuSubTrigger: () => (/* binding */ MenuSubTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   SubContent: () => (/* binding */ SubContent),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger),\n/* harmony export */   createMenuScope: () => (/* binding */ createMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@1_g2rahi25b6vuxt66qpykugaxdq/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types_ed6g35xmswrdo4rpjcoc2ylqq4/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_2fbc3wi7jfrrglulhiq3cho3yi/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3._wwc252xynq3agan6q2g4prz53u/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3._vj5bwhvxp26kuufjjrp4d4yqwm/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18._mcnf744msur4vp7w5dm65hzhsi/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18_ujcax4x2btumxxy334cxx2hjfq/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types+reac_fkad3ywkl2psdzndyckeemdxa4/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.0_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,CheckboxItem,Content,Group,Item,ItemIndicator,Label,Menu,MenuAnchor,MenuArrow,MenuCheckboxItem,MenuContent,MenuGroup,MenuItem,MenuItemIndicator,MenuLabel,MenuPortal,MenuRadioGroup,MenuRadioItem,MenuSeparator,MenuSub,MenuSubContent,MenuSubTrigger,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,createMenuScope auto */ // src/menu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar SELECTION_KEYS = [\n    \"Enter\",\n    \" \"\n];\nvar FIRST_KEYS = [\n    \"ArrowDown\",\n    \"PageUp\",\n    \"Home\"\n];\nvar LAST_KEYS = [\n    \"ArrowUp\",\n    \"PageDown\",\n    \"End\"\n];\nvar FIRST_LAST_KEYS = [\n    ...FIRST_KEYS,\n    ...LAST_KEYS\n];\nvar SUB_OPEN_KEYS = {\n    ltr: [\n        ...SELECTION_KEYS,\n        \"ArrowRight\"\n    ],\n    rtl: [\n        ...SELECTION_KEYS,\n        \"ArrowLeft\"\n    ]\n};\nvar SUB_CLOSE_KEYS = {\n    ltr: [\n        \"ArrowLeft\"\n    ],\n    rtl: [\n        \"ArrowRight\"\n    ]\n};\nvar MENU_NAME = \"Menu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(MENU_NAME);\nvar [createMenuContext, createMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(MENU_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope,\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope)();\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope)();\nvar [MenuProvider, useMenuContext] = createMenuContext(MENU_NAME);\nvar [MenuRootProvider, useMenuRootContext] = createMenuContext(MENU_NAME);\nvar Menu = (props)=>{\n    const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isUsingKeyboardRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = ()=>{\n            isUsingKeyboardRef.current = true;\n            document.addEventListener(\"pointerdown\", handlePointer, {\n                capture: true,\n                once: true\n            });\n            document.addEventListener(\"pointermove\", handlePointer, {\n                capture: true,\n                once: true\n            });\n        };\n        const handlePointer = ()=>isUsingKeyboardRef.current = false;\n        document.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n            document.removeEventListener(\"pointerdown\", handlePointer, {\n                capture: true\n            });\n            document.removeEventListener(\"pointermove\", handlePointer, {\n                capture: true\n            });\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootProvider, {\n                scope: __scopeMenu,\n                onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>handleOpenChange(false), [\n                    handleOpenChange\n                ]),\n                isUsingKeyboardRef,\n                dir: direction,\n                modal,\n                children\n            })\n        })\n    });\n};\nMenu.displayName = MENU_NAME;\nvar ANCHOR_NAME = \"MenuAnchor\";\nvar MenuAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nMenuAnchor.displayName = ANCHOR_NAME;\nvar PORTAL_NAME = \"MenuPortal\";\nvar [PortalProvider, usePortalContext] = createMenuContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar MenuPortal = (props)=>{\n    const { __scopeMenu, forceMount, children, container } = props;\n    const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeMenu,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"MenuContent\";\nvar [MenuContentProvider, useMenuContentContext] = createMenuContext(CONTENT_NAME);\nvar MenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: rootContext.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentNonModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                })\n            })\n        })\n    });\n});\nvar MenuRootContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = ref.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_11__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: context.open,\n        disableOutsideScroll: true,\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n            checkForDefaultPrevented: false\n        }),\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar MenuRootContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        disableOutsideScroll: false,\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__.createSlot)(\"MenuContent.ScrollLock\");\nvar MenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, loop = false, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEntryFocus, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, disableOutsideScroll, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const pointerGraceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerGraceIntentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerDirRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"right\");\n    const lastPointerXRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const ScrollLockWrapper = disableOutsideScroll ? react_remove_scroll__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll ? {\n        as: Slot,\n        allowPinchZoom: true\n    } : void 0;\n    const handleTypeaheadSearch = (key)=>{\n        const search = searchRef.current + key;\n        const items = getItems().filter((item)=>!item.disabled);\n        const currentItem = document.activeElement;\n        const currentMatch = items.find((item)=>item.ref.current === currentItem)?.textValue;\n        const values = items.map((item)=>item.textValue);\n        const nextMatch = getNextMatch(values, search, currentMatch);\n        const newItem = items.find((item)=>item.textValue === nextMatch)?.ref.current;\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n        if (newItem) {\n            setTimeout(()=>newItem.focus());\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const isPointerMovingToSubmenu = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n        return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentProvider, {\n        scope: __scopeMenu,\n        searchRef,\n        onItemEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        onItemLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((intent)=>{\n            pointerGraceIntentRef.current = intent;\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollLockWrapper, {\n            ...scrollLockWrapperProps,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__.FocusScope, {\n                asChild: true,\n                trapped: trapFocus,\n                onMountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onOpenAutoFocus, (event)=>{\n                    event.preventDefault();\n                    contentRef.current?.focus({\n                        preventScroll: true\n                    });\n                }),\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside,\n                    onInteractOutside,\n                    onDismiss,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Root, {\n                        asChild: true,\n                        ...rovingFocusGroupScope,\n                        dir: rootContext.dir,\n                        orientation: \"vertical\",\n                        loop,\n                        currentTabStopId: currentItemId,\n                        onCurrentTabStopIdChange: setCurrentItemId,\n                        onEntryFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEntryFocus, (event)=>{\n                            if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                        }),\n                        preventScrollOnEntryFocus: true,\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                            role: \"menu\",\n                            \"aria-orientation\": \"vertical\",\n                            \"data-state\": getOpenState(context.open),\n                            \"data-radix-menu-content\": \"\",\n                            dir: rootContext.dir,\n                            ...popperScope,\n                            ...contentProps,\n                            ref: composedRefs,\n                            style: {\n                                outline: \"none\",\n                                ...contentProps.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                                const target = event.target;\n                                const isKeyDownInside = target.closest(\"[data-radix-menu-content]\") === event.currentTarget;\n                                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                                const isCharacterKey = event.key.length === 1;\n                                if (isKeyDownInside) {\n                                    if (event.key === \"Tab\") event.preventDefault();\n                                    if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                                }\n                                const content = contentRef.current;\n                                if (event.target !== content) return;\n                                if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                                event.preventDefault();\n                                const items = getItems().filter((item)=>!item.disabled);\n                                const candidateNodes = items.map((item)=>item.ref.current);\n                                if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                                focusFirst(candidateNodes);\n                            }),\n                            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, (event)=>{\n                                if (!event.currentTarget.contains(event.target)) {\n                                    window.clearTimeout(timerRef.current);\n                                    searchRef.current = \"\";\n                                }\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                                const target = event.target;\n                                const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n                                if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                                    const newDir = event.clientX > lastPointerXRef.current ? \"right\" : \"left\";\n                                    pointerDirRef.current = newDir;\n                                    lastPointerXRef.current = event.clientX;\n                                }\n                            }))\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"MenuGroup\";\nvar MenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...groupProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"group\",\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"MenuLabel\";\nvar MenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...labelProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"MenuItem\";\nvar ITEM_SELECT = \"menu.itemSelect\";\nvar MenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleSelect = ()=>{\n        const menuItem = ref.current;\n        if (!disabled && menuItem) {\n            const itemSelectEvent = new CustomEvent(ITEM_SELECT, {\n                bubbles: true,\n                cancelable: true\n            });\n            menuItem.addEventListener(ITEM_SELECT, (event)=>onSelect?.(event), {\n                once: true\n            });\n            (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.dispatchDiscreteCustomEvent)(menuItem, itemSelectEvent);\n            if (itemSelectEvent.defaultPrevented) {\n                isPointerDownRef.current = false;\n            } else {\n                rootContext.onClose();\n            }\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n        ...itemProps,\n        ref: composedRefs,\n        disabled,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, handleSelect),\n        onPointerDown: (event)=>{\n            props.onPointerDown?.(event);\n            isPointerDownRef.current = true;\n        },\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            if (!isPointerDownRef.current) event.currentTarget?.click();\n        }),\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            const isTypingAhead = contentContext.searchRef.current !== \"\";\n            if (disabled || isTypingAhead && event.key === \" \") return;\n            if (SELECTION_KEYS.includes(event.key)) {\n                event.currentTarget.click();\n                event.preventDefault();\n            }\n        })\n    });\n});\nMenuItem.displayName = ITEM_NAME;\nvar MenuItemImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [textContent, setTextContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const menuItem = ref.current;\n        if (menuItem) {\n            setTextContent((menuItem.textContent ?? \"\").trim());\n        }\n    }, [\n        itemProps.children\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeMenu,\n        disabled,\n        textValue: textValue ?? textContent,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Item, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            focusable: !disabled,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n                role: \"menuitem\",\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                ...itemProps,\n                ref: composedRefs,\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                    if (disabled) {\n                        contentContext.onItemLeave(event);\n                    } else {\n                        contentContext.onItemEnter(event);\n                        if (!event.defaultPrevented) {\n                            const item = event.currentTarget;\n                            item.focus({\n                                preventScroll: true\n                            });\n                        }\n                    }\n                })),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>contentContext.onItemLeave(event))),\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, ()=>setIsFocused(false))\n            })\n        })\n    });\n});\nvar CHECKBOX_ITEM_NAME = \"MenuCheckboxItem\";\nvar MenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemcheckbox\",\n            \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n            ...checkboxItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(checkboxItemProps.onSelect, ()=>onCheckedChange?.(isIndeterminate(checked) ? true : !checked), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"MenuRadioGroup\";\nvar [RadioGroupProvider, useRadioGroupContext] = createMenuContext(RADIO_GROUP_NAME, {\n    value: void 0,\n    onValueChange: ()=>{}\n});\nvar MenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onValueChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: props.__scopeMenu,\n        value,\n        onValueChange: handleValueChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuGroup, {\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"MenuRadioItem\";\nvar MenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemradio\",\n            \"aria-checked\": checked,\n            ...radioItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(radioItemProps.onSelect, ()=>context.onValueChange?.(value), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar ITEM_INDICATOR_NAME = \"MenuItemIndicator\";\nvar [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext(ITEM_INDICATOR_NAME, {\n    checked: false\n});\nvar MenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || isIndeterminate(indicatorContext.checked) || indicatorContext.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.span, {\n            ...itemIndicatorProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(indicatorContext.checked)\n        })\n    });\n});\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SEPARATOR_NAME = \"MenuSeparator\";\nvar MenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"separator\",\n        \"aria-orientation\": \"horizontal\",\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"MenuArrow\";\nvar MenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nMenuArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"MenuSub\";\nvar [MenuSubProvider, useMenuSubContext] = createMenuContext(SUB_NAME);\nvar MenuSub = (props)=>{\n    const { __scopeMenu, children, open = false, onOpenChange } = props;\n    const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (parentMenuContext.open === false) handleOpenChange(false);\n        return ()=>handleOpenChange(false);\n    }, [\n        parentMenuContext.open,\n        handleOpenChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuSubProvider, {\n                scope: __scopeMenu,\n                contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                trigger,\n                onTriggerChange: setTrigger,\n                children\n            })\n        })\n    });\n};\nMenuSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"MenuSubTrigger\";\nvar MenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = {\n        __scopeMenu: props.__scopeMenu\n    };\n    const clearOpenTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = null;\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>clearOpenTimer, [\n        clearOpenTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const pointerGraceTimer = pointerGraceTimerRef.current;\n        return ()=>{\n            window.clearTimeout(pointerGraceTimer);\n            onPointerGraceIntentChange(null);\n        };\n    }, [\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuAnchor, {\n        asChild: true,\n        ...scope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n            id: subContext.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": subContext.contentId,\n            \"data-state\": getOpenState(context.open),\n            ...props,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.composeRefs)(forwardedRef, subContext.onTriggerChange),\n            onClick: (event)=>{\n                props.onClick?.(event);\n                if (props.disabled || event.defaultPrevented) return;\n                event.currentTarget.focus();\n                if (!context.open) context.onOpenChange(true);\n            },\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                contentContext.onItemEnter(event);\n                if (event.defaultPrevented) return;\n                if (!props.disabled && !context.open && !openTimerRef.current) {\n                    contentContext.onPointerGraceIntentChange(null);\n                    openTimerRef.current = window.setTimeout(()=>{\n                        context.onOpenChange(true);\n                        clearOpenTimer();\n                    }, 100);\n                }\n            })),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>{\n                clearOpenTimer();\n                const contentRect = context.content?.getBoundingClientRect();\n                if (contentRect) {\n                    const side = context.content?.dataset.side;\n                    const rightSide = side === \"right\";\n                    const bleed = rightSide ? -5 : 5;\n                    const contentNearEdge = contentRect[rightSide ? \"left\" : \"right\"];\n                    const contentFarEdge = contentRect[rightSide ? \"right\" : \"left\"];\n                    contentContext.onPointerGraceIntentChange({\n                        area: [\n                            // Apply a bleed on clientX to ensure that our exit point is\n                            // consistently within polygon bounds\n                            {\n                                x: event.clientX + bleed,\n                                y: event.clientY\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.bottom\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.bottom\n                            }\n                        ],\n                        side\n                    });\n                    window.clearTimeout(pointerGraceTimerRef.current);\n                    pointerGraceTimerRef.current = window.setTimeout(()=>contentContext.onPointerGraceIntentChange(null), 300);\n                } else {\n                    contentContext.onTriggerLeave(event);\n                    if (event.defaultPrevented) return;\n                    contentContext.onPointerGraceIntentChange(null);\n                }\n            })),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isTypingAhead = contentContext.searchRef.current !== \"\";\n                if (props.disabled || isTypingAhead && event.key === \" \") return;\n                if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n                    context.onOpenChange(true);\n                    context.content?.focus();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"MenuSubContent\";\nvar MenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n                    id: subContext.contentId,\n                    \"aria-labelledby\": subContext.triggerId,\n                    ...subContentProps,\n                    ref: composedRefs,\n                    align: \"start\",\n                    side: rootContext.dir === \"rtl\" ? \"left\" : \"right\",\n                    disableOutsidePointerEvents: false,\n                    disableOutsideScroll: false,\n                    trapFocus: false,\n                    onOpenAutoFocus: (event)=>{\n                        if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                        event.preventDefault();\n                    },\n                    onCloseAutoFocus: (event)=>event.preventDefault(),\n                    onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                        if (event.target !== subContext.trigger) context.onOpenChange(false);\n                    }),\n                    onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (event)=>{\n                        rootContext.onClose();\n                        event.preventDefault();\n                    }),\n                    onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                        const isKeyDownInside = event.currentTarget.contains(event.target);\n                        const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                        if (isKeyDownInside && isCloseKey) {\n                            context.onOpenChange(false);\n                            subContext.trigger?.focus();\n                            event.preventDefault();\n                        }\n                    })\n                })\n            })\n        })\n    });\n});\nMenuSubContent.displayName = SUB_CONTENT_NAME;\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getCheckedState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nfunction focusFirst(candidates) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus();\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nfunction getNextMatch(values, search, currentMatch) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n    let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n    const excludeCurrentMatch = normalizedSearch.length === 1;\n    if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v)=>v !== currentMatch);\n    const nextMatch = wrappedValues.find((value)=>value.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextMatch !== currentMatch ? nextMatch : void 0;\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction isPointerInGraceArea(event, area) {\n    if (!area) return false;\n    const cursorPos = {\n        x: event.clientX,\n        y: event.clientY\n    };\n    return isPointInPolygon(cursorPos, area);\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root3 = Menu;\nvar Anchor2 = MenuAnchor;\nvar Portal = MenuPortal;\nvar Content2 = MenuContent;\nvar Group = MenuGroup;\nvar Label = MenuLabel;\nvar Item2 = MenuItem;\nvar CheckboxItem = MenuCheckboxItem;\nvar RadioGroup = MenuRadioGroup;\nvar RadioItem = MenuRadioItem;\nvar ItemIndicator = MenuItemIndicator;\nvar Separator = MenuSeparator;\nvar Arrow2 = MenuArrow;\nvar Sub = MenuSub;\nvar SubTrigger = MenuSubTrigger;\nvar SubContent = MenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.15_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3.2_apm2kfxcvbaumwrlajepl7cp2e/node_modules/@radix-ui/react-menu/dist/index.mjs\n");

/***/ })

};
;