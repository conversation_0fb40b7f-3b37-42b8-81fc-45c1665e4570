"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-navigation-menu@1.2.13_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_nzbxb4sdy4hop7ix5sg6bnjfmi";
exports.ids = ["vendor-chunks/@radix-ui+react-navigation-menu@1.2.13_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_nzbxb4sdy4hop7ix5sg6bnjfmi"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-navigation-menu@1.2.13_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_nzbxb4sdy4hop7ix5sg6bnjfmi/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-navigation-menu@1.2.13_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_nzbxb4sdy4hop7ix5sg6bnjfmi/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   List: () => (/* binding */ List),\n/* harmony export */   NavigationMenu: () => (/* binding */ NavigationMenu),\n/* harmony export */   NavigationMenuContent: () => (/* binding */ NavigationMenuContent),\n/* harmony export */   NavigationMenuIndicator: () => (/* binding */ NavigationMenuIndicator),\n/* harmony export */   NavigationMenuItem: () => (/* binding */ NavigationMenuItem),\n/* harmony export */   NavigationMenuLink: () => (/* binding */ NavigationMenuLink),\n/* harmony export */   NavigationMenuList: () => (/* binding */ NavigationMenuList),\n/* harmony export */   NavigationMenuSub: () => (/* binding */ NavigationMenuSub),\n/* harmony export */   NavigationMenuTrigger: () => (/* binding */ NavigationMenuTrigger),\n/* harmony export */   NavigationMenuViewport: () => (/* binding */ NavigationMenuViewport),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createNavigationMenuScope: () => (/* binding */ createNavigationMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18_ujcax4x2btumxxy334cxx2hjfq/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18._mcnf744msur4vp7w5dm65hzhsi/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@1_g2rahi25b6vuxt66qpykugaxdq/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types_ed6g35xmswrdo4rpjcoc2ylqq4/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previous@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_ai4rarwltg7o2u2yozjp5gcjt4/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Content,Indicator,Item,Link,List,NavigationMenu,NavigationMenuContent,NavigationMenuIndicator,NavigationMenuItem,NavigationMenuLink,NavigationMenuList,NavigationMenuSub,NavigationMenuTrigger,NavigationMenuViewport,Root,Sub,Trigger,Viewport,createNavigationMenuScope auto */ // src/navigation-menu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar NAVIGATION_MENU_NAME = \"NavigationMenu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(NAVIGATION_MENU_NAME);\nvar [FocusGroupCollection, useFocusGroupCollection, createFocusGroupCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(NAVIGATION_MENU_NAME);\nvar [createNavigationMenuContext, createNavigationMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(NAVIGATION_MENU_NAME, [\n    createCollectionScope,\n    createFocusGroupCollectionScope\n]);\nvar [NavigationMenuProviderImpl, useNavigationMenuContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar [ViewportContentProvider, useViewportContentContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar NavigationMenu = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, onValueChange, defaultValue, delayDuration = 200, skipDelayDuration = 300, orientation = \"horizontal\", dir, ...NavigationMenuProps } = props;\n    const [navigationMenu, setNavigationMenu] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node)=>setNavigationMenu(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [isOpenDelayed, setIsOpenDelayed] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        onChange: (value2)=>{\n            const isOpen = value2 !== \"\";\n            const hasSkipDelayDuration = skipDelayDuration > 0;\n            if (isOpen) {\n                window.clearTimeout(skipDelayTimerRef.current);\n                if (hasSkipDelayDuration) setIsOpenDelayed(false);\n            } else {\n                window.clearTimeout(skipDelayTimerRef.current);\n                skipDelayTimerRef.current = window.setTimeout(()=>setIsOpenDelayed(true), skipDelayDuration);\n            }\n            onValueChange?.(value2);\n        },\n        defaultProp: defaultValue ?? \"\",\n        caller: NAVIGATION_MENU_NAME\n    });\n    const startCloseTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerRef.current = window.setTimeout(()=>setValue(\"\"), 150);\n    }, [\n        setValue\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>{\n        window.clearTimeout(closeTimerRef.current);\n        setValue(itemValue);\n    }, [\n        setValue\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>{\n        const isOpenItem = value === itemValue;\n        if (isOpenItem) {\n            window.clearTimeout(closeTimerRef.current);\n        } else {\n            openTimerRef.current = window.setTimeout(()=>{\n                window.clearTimeout(closeTimerRef.current);\n                setValue(itemValue);\n            }, delayDuration);\n        }\n    }, [\n        value,\n        setValue,\n        delayDuration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            window.clearTimeout(openTimerRef.current);\n            window.clearTimeout(closeTimerRef.current);\n            window.clearTimeout(skipDelayTimerRef.current);\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProvider, {\n        scope: __scopeNavigationMenu,\n        isRootMenu: true,\n        value,\n        dir: direction,\n        orientation,\n        rootNavigationMenu: navigationMenu,\n        onTriggerEnter: (itemValue)=>{\n            window.clearTimeout(openTimerRef.current);\n            if (isOpenDelayed) handleDelayedOpen(itemValue);\n            else handleOpen(itemValue);\n        },\n        onTriggerLeave: ()=>{\n            window.clearTimeout(openTimerRef.current);\n            startCloseTimer();\n        },\n        onContentEnter: ()=>window.clearTimeout(closeTimerRef.current),\n        onContentLeave: startCloseTimer,\n        onItemSelect: (itemValue)=>{\n            setValue((prevValue)=>prevValue === itemValue ? \"\" : itemValue);\n        },\n        onItemDismiss: ()=>setValue(\"\"),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.nav, {\n            \"aria-label\": \"Main\",\n            \"data-orientation\": orientation,\n            dir: direction,\n            ...NavigationMenuProps,\n            ref: composedRef\n        })\n    });\n});\nNavigationMenu.displayName = NAVIGATION_MENU_NAME;\nvar SUB_NAME = \"NavigationMenuSub\";\nvar NavigationMenuSub = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, onValueChange, defaultValue, orientation = \"horizontal\", ...subProps } = props;\n    const context = useNavigationMenuContext(SUB_NAME, __scopeNavigationMenu);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        onChange: onValueChange,\n        defaultProp: defaultValue ?? \"\",\n        caller: SUB_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProvider, {\n        scope: __scopeNavigationMenu,\n        isRootMenu: false,\n        value,\n        dir: context.dir,\n        orientation,\n        rootNavigationMenu: context.rootNavigationMenu,\n        onTriggerEnter: (itemValue)=>setValue(itemValue),\n        onItemSelect: (itemValue)=>setValue(itemValue),\n        onItemDismiss: ()=>setValue(\"\"),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            \"data-orientation\": orientation,\n            ...subProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuSub.displayName = SUB_NAME;\nvar NavigationMenuProvider = (props)=>{\n    const { scope, isRootMenu, rootNavigationMenu, dir, orientation, children, value, onItemSelect, onItemDismiss, onTriggerEnter, onTriggerLeave, onContentEnter, onContentLeave } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewportContent, setViewportContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Map());\n    const [indicatorTrack, setIndicatorTrack] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProviderImpl, {\n        scope,\n        isRootMenu,\n        rootNavigationMenu,\n        value,\n        previousValue: (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__.usePrevious)(value),\n        baseId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)(),\n        dir,\n        orientation,\n        viewport,\n        onViewportChange: setViewport,\n        indicatorTrack,\n        onIndicatorTrackChange: setIndicatorTrack,\n        onTriggerEnter: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onTriggerEnter),\n        onTriggerLeave: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onTriggerLeave),\n        onContentEnter: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onContentEnter),\n        onContentLeave: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onContentLeave),\n        onItemSelect: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onItemSelect),\n        onItemDismiss: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onItemDismiss),\n        onViewportContentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((contentValue, contentData)=>{\n            setViewportContent((prevContent)=>{\n                prevContent.set(contentValue, contentData);\n                return new Map(prevContent);\n            });\n        }, []),\n        onViewportContentRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((contentValue)=>{\n            setViewportContent((prevContent)=>{\n                if (!prevContent.has(contentValue)) return prevContent;\n                prevContent.delete(contentValue);\n                return new Map(prevContent);\n            });\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n            scope,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ViewportContentProvider, {\n                scope,\n                items: viewportContent,\n                children\n            })\n        })\n    });\n};\nvar LIST_NAME = \"NavigationMenuList\";\nvar NavigationMenuList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...listProps } = props;\n    const context = useNavigationMenuContext(LIST_NAME, __scopeNavigationMenu);\n    const list = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.ul, {\n        \"data-orientation\": context.orientation,\n        ...listProps,\n        ref: forwardedRef\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        style: {\n            position: \"relative\"\n        },\n        ref: context.onIndicatorTrackChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n            scope: __scopeNavigationMenu,\n            children: context.isRootMenu ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroup, {\n                asChild: true,\n                children: list\n            }) : list\n        })\n    });\n});\nNavigationMenuList.displayName = LIST_NAME;\nvar ITEM_NAME = \"NavigationMenuItem\";\nvar [NavigationMenuItemContextProvider, useNavigationMenuItemContext] = createNavigationMenuContext(ITEM_NAME);\nvar NavigationMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, ...itemProps } = props;\n    const autoValue = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const value = valueProp || autoValue || \"LEGACY_REACT_AUTO_VALUE\";\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const focusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const restoreContentTabOrderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    const wasEscapeCloseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleContentEntry = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((side = \"start\")=>{\n        if (contentRef.current) {\n            restoreContentTabOrderRef.current();\n            const candidates = getTabbableCandidates(contentRef.current);\n            if (candidates.length) focusFirst(side === \"start\" ? candidates : candidates.reverse());\n        }\n    }, []);\n    const handleContentExit = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (contentRef.current) {\n            const candidates = getTabbableCandidates(contentRef.current);\n            if (candidates.length) restoreContentTabOrderRef.current = removeFromTabOrder(candidates);\n        }\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuItemContextProvider, {\n        scope: __scopeNavigationMenu,\n        value,\n        triggerRef,\n        contentRef,\n        focusProxyRef,\n        wasEscapeCloseRef,\n        onEntryKeyDown: handleContentEntry,\n        onFocusProxyEnter: handleContentEntry,\n        onRootContentClose: handleContentExit,\n        onContentFocusOutside: handleContentExit,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.li, {\n            ...itemProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuItem.displayName = ITEM_NAME;\nvar TRIGGER_NAME = \"NavigationMenuTrigger\";\nvar NavigationMenuTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, disabled, ...triggerProps } = props;\n    const context = useNavigationMenuContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n    const itemContext = useNavigationMenuItemContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(ref, itemContext.triggerRef, forwardedRef);\n    const triggerId = makeTriggerId(context.baseId, itemContext.value);\n    const contentId = makeContentId(context.baseId, itemContext.value);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const wasClickCloseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const open = itemContext.value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                scope: __scopeNavigationMenu,\n                value: itemContext.value,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupItem, {\n                    asChild: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.button, {\n                        id: triggerId,\n                        disabled,\n                        \"data-disabled\": disabled ? \"\" : void 0,\n                        \"data-state\": getOpenState(open),\n                        \"aria-expanded\": open,\n                        \"aria-controls\": contentId,\n                        ...triggerProps,\n                        ref: composedRefs,\n                        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, ()=>{\n                            wasClickCloseRef.current = false;\n                            itemContext.wasEscapeCloseRef.current = false;\n                        }),\n                        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse(()=>{\n                            if (disabled || wasClickCloseRef.current || itemContext.wasEscapeCloseRef.current || hasPointerMoveOpenedRef.current) return;\n                            context.onTriggerEnter(itemContext.value);\n                            hasPointerMoveOpenedRef.current = true;\n                        })),\n                        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(()=>{\n                            if (disabled) return;\n                            context.onTriggerLeave();\n                            hasPointerMoveOpenedRef.current = false;\n                        })),\n                        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, ()=>{\n                            context.onItemSelect(itemContext.value);\n                            wasClickCloseRef.current = open;\n                        }),\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                            const verticalEntryKey = context.dir === \"rtl\" ? \"ArrowLeft\" : \"ArrowRight\";\n                            const entryKey = {\n                                horizontal: \"ArrowDown\",\n                                vertical: verticalEntryKey\n                            }[context.orientation];\n                            if (open && event.key === entryKey) {\n                                itemContext.onEntryKeyDown();\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            }),\n            open && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        \"aria-hidden\": true,\n                        tabIndex: 0,\n                        ref: itemContext.focusProxyRef,\n                        onFocus: (event)=>{\n                            const content = itemContext.contentRef.current;\n                            const prevFocusedElement = event.relatedTarget;\n                            const wasTriggerFocused = prevFocusedElement === ref.current;\n                            const wasFocusFromContent = content?.contains(prevFocusedElement);\n                            if (wasTriggerFocused || !wasFocusFromContent) {\n                                itemContext.onFocusProxyEnter(wasTriggerFocused ? \"start\" : \"end\");\n                            }\n                        }\n                    }),\n                    context.viewport && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"span\", {\n                        \"aria-owns\": contentId\n                    })\n                ]\n            })\n        ]\n    });\n});\nNavigationMenuTrigger.displayName = TRIGGER_NAME;\nvar LINK_NAME = \"NavigationMenuLink\";\nvar LINK_SELECT = \"navigationMenu.linkSelect\";\nvar NavigationMenuLink = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, active, onSelect, ...linkProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupItem, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.a, {\n            \"data-active\": active ? \"\" : void 0,\n            \"aria-current\": active ? \"page\" : void 0,\n            ...linkProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, (event)=>{\n                const target = event.target;\n                const linkSelectEvent = new CustomEvent(LINK_SELECT, {\n                    bubbles: true,\n                    cancelable: true\n                });\n                target.addEventListener(LINK_SELECT, (event2)=>onSelect?.(event2), {\n                    once: true\n                });\n                (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.dispatchDiscreteCustomEvent)(target, linkSelectEvent);\n                if (!linkSelectEvent.defaultPrevented && !event.metaKey) {\n                    const rootContentDismissEvent = new CustomEvent(ROOT_CONTENT_DISMISS, {\n                        bubbles: true,\n                        cancelable: true\n                    });\n                    (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.dispatchDiscreteCustomEvent)(target, rootContentDismissEvent);\n                }\n            }, {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nNavigationMenuLink.displayName = LINK_NAME;\nvar INDICATOR_NAME = \"NavigationMenuIndicator\";\nvar NavigationMenuIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...indicatorProps } = props;\n    const context = useNavigationMenuContext(INDICATOR_NAME, props.__scopeNavigationMenu);\n    const isVisible = Boolean(context.value);\n    return context.indicatorTrack ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || isVisible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuIndicatorImpl, {\n            ...indicatorProps,\n            ref: forwardedRef\n        })\n    }), context.indicatorTrack) : null;\n});\nNavigationMenuIndicator.displayName = INDICATOR_NAME;\nvar NavigationMenuIndicatorImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...indicatorProps } = props;\n    const context = useNavigationMenuContext(INDICATOR_NAME, __scopeNavigationMenu);\n    const getItems = useCollection(__scopeNavigationMenu);\n    const [activeTrigger, setActiveTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [position, setPosition] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isHorizontal = context.orientation === \"horizontal\";\n    const isVisible = Boolean(context.value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const items = getItems();\n        const triggerNode = items.find((item)=>item.value === context.value)?.ref.current;\n        if (triggerNode) setActiveTrigger(triggerNode);\n    }, [\n        getItems,\n        context.value\n    ]);\n    const handlePositionChange = ()=>{\n        if (activeTrigger) {\n            setPosition({\n                size: isHorizontal ? activeTrigger.offsetWidth : activeTrigger.offsetHeight,\n                offset: isHorizontal ? activeTrigger.offsetLeft : activeTrigger.offsetTop\n            });\n        }\n    };\n    useResizeObserver(activeTrigger, handlePositionChange);\n    useResizeObserver(context.indicatorTrack, handlePositionChange);\n    return position ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        \"aria-hidden\": true,\n        \"data-state\": isVisible ? \"visible\" : \"hidden\",\n        \"data-orientation\": context.orientation,\n        ...indicatorProps,\n        ref: forwardedRef,\n        style: {\n            position: \"absolute\",\n            ...isHorizontal ? {\n                left: 0,\n                width: position.size + \"px\",\n                transform: `translateX(${position.offset}px)`\n            } : {\n                top: 0,\n                height: position.size + \"px\",\n                transform: `translateY(${position.offset}px)`\n            },\n            ...indicatorProps.style\n        }\n    }) : null;\n});\nvar CONTENT_NAME = \"NavigationMenuContent\";\nvar NavigationMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...contentProps } = props;\n    const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const itemContext = useNavigationMenuItemContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(itemContext.contentRef, forwardedRef);\n    const open = itemContext.value === context.value;\n    const commonProps = {\n        value: itemContext.value,\n        triggerRef: itemContext.triggerRef,\n        focusProxyRef: itemContext.focusProxyRef,\n        wasEscapeCloseRef: itemContext.wasEscapeCloseRef,\n        onContentFocusOutside: itemContext.onContentFocusOutside,\n        onRootContentClose: itemContext.onRootContentClose,\n        ...contentProps\n    };\n    return !context.viewport ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuContentImpl, {\n            \"data-state\": getOpenState(open),\n            ...commonProps,\n            ref: composedRefs,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, context.onContentEnter),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(context.onContentLeave)),\n            style: {\n                // Prevent interaction when animating out\n                pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n                ...commonProps.style\n            }\n        })\n    }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ViewportContentMounter, {\n        forceMount,\n        ...commonProps,\n        ref: composedRefs\n    });\n});\nNavigationMenuContent.displayName = CONTENT_NAME;\nvar ViewportContentMounter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const { onViewportContentChange, onViewportContentRemove } = context;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        onViewportContentChange(props.value, {\n            ref: forwardedRef,\n            ...props\n        });\n    }, [\n        props,\n        forwardedRef,\n        onViewportContentChange\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        return ()=>onViewportContentRemove(props.value);\n    }, [\n        props.value,\n        onViewportContentRemove\n    ]);\n    return null;\n});\nvar ROOT_CONTENT_DISMISS = \"navigationMenu.rootContentDismiss\";\nvar NavigationMenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value, triggerRef, focusProxyRef, wasEscapeCloseRef, onRootContentClose, onContentFocusOutside, ...contentProps } = props;\n    const context = useNavigationMenuContext(CONTENT_NAME, __scopeNavigationMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(ref, forwardedRef);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const getItems = useCollection(__scopeNavigationMenu);\n    const prevMotionAttributeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { onItemDismiss } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = ref.current;\n        if (context.isRootMenu && content) {\n            const handleClose = ()=>{\n                onItemDismiss();\n                onRootContentClose();\n                if (content.contains(document.activeElement)) triggerRef.current?.focus();\n            };\n            content.addEventListener(ROOT_CONTENT_DISMISS, handleClose);\n            return ()=>content.removeEventListener(ROOT_CONTENT_DISMISS, handleClose);\n        }\n    }, [\n        context.isRootMenu,\n        props.value,\n        triggerRef,\n        onItemDismiss,\n        onRootContentClose\n    ]);\n    const motionAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const items = getItems();\n        const values = items.map((item)=>item.value);\n        if (context.dir === \"rtl\") values.reverse();\n        const index = values.indexOf(context.value);\n        const prevIndex = values.indexOf(context.previousValue);\n        const isSelected = value === context.value;\n        const wasSelected = prevIndex === values.indexOf(value);\n        if (!isSelected && !wasSelected) return prevMotionAttributeRef.current;\n        const attribute = (()=>{\n            if (index !== prevIndex) {\n                if (isSelected && prevIndex !== -1) return index > prevIndex ? \"from-end\" : \"from-start\";\n                if (wasSelected && index !== -1) return index > prevIndex ? \"to-start\" : \"to-end\";\n            }\n            return null;\n        })();\n        prevMotionAttributeRef.current = attribute;\n        return attribute;\n    }, [\n        context.previousValue,\n        context.value,\n        context.dir,\n        getItems,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroup, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__.DismissableLayer, {\n            id: contentId,\n            \"aria-labelledby\": triggerId,\n            \"data-motion\": motionAttribute,\n            \"data-orientation\": context.orientation,\n            ...contentProps,\n            ref: composedRefs,\n            disableOutsidePointerEvents: false,\n            onDismiss: ()=>{\n                const rootContentDismissEvent = new Event(ROOT_CONTENT_DISMISS, {\n                    bubbles: true,\n                    cancelable: true\n                });\n                ref.current?.dispatchEvent(rootContentDismissEvent);\n            },\n            onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                onContentFocusOutside();\n                const target = event.target;\n                if (context.rootNavigationMenu?.contains(target)) event.preventDefault();\n            }),\n            onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n                const target = event.target;\n                const isTrigger = getItems().some((item)=>item.ref.current?.contains(target));\n                const isRootViewport = context.isRootMenu && context.viewport?.contains(target);\n                if (isTrigger || isRootViewport || !context.isRootMenu) event.preventDefault();\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const candidates = getTabbableCandidates(event.currentTarget);\n                    const focusedElement = document.activeElement;\n                    const index = candidates.findIndex((candidate)=>candidate === focusedElement);\n                    const isMovingBackwards = event.shiftKey;\n                    const nextCandidates = isMovingBackwards ? candidates.slice(0, index).reverse() : candidates.slice(index + 1, candidates.length);\n                    if (focusFirst(nextCandidates)) {\n                        event.preventDefault();\n                    } else {\n                        focusProxyRef.current?.focus();\n                    }\n                }\n            }),\n            onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (_event)=>{\n                wasEscapeCloseRef.current = true;\n            })\n        })\n    });\n});\nvar VIEWPORT_NAME = \"NavigationMenuViewport\";\nvar NavigationMenuViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...viewportProps } = props;\n    const context = useNavigationMenuContext(VIEWPORT_NAME, props.__scopeNavigationMenu);\n    const open = Boolean(context.value);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuViewportImpl, {\n            ...viewportProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuViewport.displayName = VIEWPORT_NAME;\nvar NavigationMenuViewportImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, children, ...viewportImplProps } = props;\n    const context = useNavigationMenuContext(VIEWPORT_NAME, __scopeNavigationMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.onViewportChange);\n    const viewportContentContext = useViewportContentContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const viewportWidth = size ? size?.width + \"px\" : void 0;\n    const viewportHeight = size ? size?.height + \"px\" : void 0;\n    const open = Boolean(context.value);\n    const activeContentValue = open ? context.value : context.previousValue;\n    const handleSizeChange = ()=>{\n        if (content) setSize({\n            width: content.offsetWidth,\n            height: content.offsetHeight\n        });\n    };\n    useResizeObserver(content, handleSizeChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        \"data-state\": getOpenState(open),\n        \"data-orientation\": context.orientation,\n        ...viewportImplProps,\n        ref: composedRefs,\n        style: {\n            // Prevent interaction when animating out\n            pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n            [\"--radix-navigation-menu-viewport-width\"]: viewportWidth,\n            [\"--radix-navigation-menu-viewport-height\"]: viewportHeight,\n            ...viewportImplProps.style\n        },\n        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, context.onContentEnter),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(context.onContentLeave)),\n        children: Array.from(viewportContentContext.items).map(([value, { ref, forceMount, ...props2 }])=>{\n            const isActive = activeContentValue === value;\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n                present: forceMount || isActive,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuContentImpl, {\n                    ...props2,\n                    ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.composeRefs)(ref, (node)=>{\n                        if (isActive && node) setContent(node);\n                    })\n                })\n            }, value);\n        })\n    });\n});\nvar FOCUS_GROUP_NAME = \"FocusGroup\";\nvar FocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const context = useNavigationMenuContext(FOCUS_GROUP_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.Provider, {\n        scope: __scopeNavigationMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.Slot, {\n            scope: __scopeNavigationMenu,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n                dir: context.dir,\n                ...groupProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar ARROW_KEYS = [\n    \"ArrowRight\",\n    \"ArrowLeft\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar FOCUS_GROUP_ITEM_NAME = \"FocusGroupItem\";\nvar FocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const getItems = useFocusGroupCollection(__scopeNavigationMenu);\n    const context = useNavigationMenuContext(FOCUS_GROUP_ITEM_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.ItemSlot, {\n        scope: __scopeNavigationMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.button, {\n            ...groupProps,\n            ref: forwardedRef,\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isFocusNavigationKey = [\n                    \"Home\",\n                    \"End\",\n                    ...ARROW_KEYS\n                ].includes(event.key);\n                if (isFocusNavigationKey) {\n                    let candidateNodes = getItems().map((item)=>item.ref.current);\n                    const prevItemKey = context.dir === \"rtl\" ? \"ArrowRight\" : \"ArrowLeft\";\n                    const prevKeys = [\n                        prevItemKey,\n                        \"ArrowUp\",\n                        \"End\"\n                    ];\n                    if (prevKeys.includes(event.key)) candidateNodes.reverse();\n                    if (ARROW_KEYS.includes(event.key)) {\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nfunction removeFromTabOrder(candidates) {\n    candidates.forEach((candidate)=>{\n        candidate.dataset.tabindex = candidate.getAttribute(\"tabindex\") || \"\";\n        candidate.setAttribute(\"tabindex\", \"-1\");\n    });\n    return ()=>{\n        candidates.forEach((candidate)=>{\n            const prevTabIndex = candidate.dataset.tabindex;\n            candidate.setAttribute(\"tabindex\", prevTabIndex);\n        });\n    };\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction makeTriggerId(baseId, value) {\n    return `${baseId}-trigger-${value}`;\n}\nfunction makeContentId(baseId, value) {\n    return `${baseId}-content-${value}`;\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root2 = NavigationMenu;\nvar Sub = NavigationMenuSub;\nvar List = NavigationMenuList;\nvar Item = NavigationMenuItem;\nvar Trigger = NavigationMenuTrigger;\nvar Link = NavigationMenuLink;\nvar Indicator = NavigationMenuIndicator;\nvar Content = NavigationMenuContent;\nvar Viewport = NavigationMenuViewport;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-navigation-menu@1.2.13_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_nzbxb4sdy4hop7ix5sg6bnjfmi/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\n");

/***/ })

};
;